{"intro": {"slide1": {"title": "Bem-vindo ao app do \nClub M Brasil.", "description": "Lorem ipsum mattis pulvinar pellentesque ultrices leo ac mattis consectetur elementum vitae posuere suscipit."}, "slide2": {"title": "Lorem ipsum dolor \nsit amet.", "description": "Lorem ipsum mattis pulvinar pellentesque ultrices leo ac mattis consectetur elementum vitae posuere suscipit."}, "slide3": {"title": "Lorem ipsum dolor \nsit amet.", "description": "Lorem ipsum mattis pulvinar pellentesque ultrices leo ac mattis consectetur elementum vitae posuere suscipit."}, "skipIntroButton": "Pular introdução", "loginButton": "<PERSON><PERSON> possuo uma conta", "skipIntroButtonLastCard": "Ir para Início"}, "login": {"title": "Bem-vindo!", "description": "É bom tê-lo conosco, insira seu CPF no campo abaixo para acessar o aplicativo.", "upsellDescription": "Insira as informações nos campos abaixo para identificarmos você.", "fields": {"document": "CPF", "documentPlaceholder": "Insira seu CPF", "password": "<PERSON><PERSON>", "passwordPlaceholder": "Insira sua senha", "fullName": "Nome completo", "fullNamePlaceholder": "Insira seu nome completo", "phone": "Telefone / WhatsApp", "phonePlaceholder": "Insira seu número de contato"}, "nextButton": "Prosseguir", "access": "Acessar", "register": {"title": "Ainda não possui cadastro?", "button": "Crie aqui."}, "forgotPassword": "<PERSON><PERSON><PERSON> <PERSON>ha senha", "goToStart": "Voltar ao início", "invalidDocument": "CPF inválido", "biometricPrompt": "Confirme que é você", "biometricFallback": "<PERSON><PERSON> se<PERSON>a", "invalidName": "Nome inválido", "invalidPhone": "Telefone inválido", "activateAccount": "Ativar Conta"}, "components": {"payment": {"creditCardForm": {"title": "Dados do Cartão", "cardNumber": "Número do Cartão", "holderName": "Nome do Portador", "holderNamePlaceholder": "Nome como está no cartão", "expirationMonth": "<PERSON><PERSON><PERSON>", "expirationYear": "<PERSON><PERSON>", "cvv": "CVV", "personalData": "<PERSON><PERSON>", "fullName": "Nome <PERSON>to", "cpfCnpj": "CPF/CNPJ", "phoneNumber": "Telefone", "addressData": "Dados de Endereço", "postalCode": "CEP", "addressNumber": "Número", "addressComplement": "Complemento", "submit": "<PERSON><PERSON>", "back": "Voltar", "errors": {"number": "Número do cartão inválido", "holderName": "Nome do portador é obrigatório", "expirationMonth": "Mês de expiração inválido", "expirationYear": "Ano de expiração inválido", "cvv": "CVV inválido", "name": "Nome completo é obrigatório", "cpfCnpj": "CPF/CNPJ é obrigatório", "phoneNumber": "Telefone é obrigatório", "postalCode": "CEP é obrigatório", "addressNumber": "Número do endereço é obrigatório"}}, "methodSelector": {"title": "<PERSON><PERSON><PERSON><PERSON>", "savedMethods": "<PERSON><PERSON><PERSON><PERSON>", "newMethods": "Novos Métodos", "savedCards": "<PERSON><PERSON><PERSON><PERSON>", "addNewCard": "Adicionar novo cartão"}, "methods": {"pix": {"title": "PIX", "description": "Pagamento instantâneo"}, "creditCard": {"title": "Cartão de Crédito", "description": "Visa, Mastercard, Elo"}, "boleto": {"title": "<PERSON><PERSON><PERSON>", "description": "Vencimento em 3 dias úteis"}, "instantPayment": "Pagamento Instantâneo", "default": "Padrão"}, "summary": {"title": "Resumo do Pedido", "entity": {"event": "Evento", "product": "Produ<PERSON>", "subscription": "Assinatura", "service": "Serviço"}, "subtotal": "Subtotal", "fees": "Taxas", "discount": "Desconto", "total": "Total", "securePayment": "🔒 Pagamento seguro e criptografado", "refundPolicy": "Política de reembolso disponível"}}}, "recoveryPassword": {"title": "<PERSON><PERSON><PERSON> <PERSON>ha senha", "description": "Enviamos o código de autenticação para o e-mail {{email}}", "securityCode": "Insira o código de segurança", "retrySendCodeLabel": "Não recebeu o código?", "retrySendCodeButton": "Reenviar código", "confirmButton": "Enviar código", "secondStepDescription": "Preencha os campos abaixo para redefinir sua senha.", "newPassword": "Nova senha", "repeatNewPassword": "<PERSON>etir nova senha", "passwordRequisition": "Obs.: <PERSON><PERSON> senha deve ter ao menos 8 caracteres.", "confirmNewPasswordButton": "<PERSON><PERSON><PERSON><PERSON>", "newPasswordPlaceholder": "Insira a nova senha", "repeatNewPasswordPlaceholder": "Insira a senha novamente"}, "recoveryPasswordSuccess": {"successTitle": "Senha redefinida com sucesso!", "successDescription": "Sua senha foi redefinida com sucesso.\nAgora, você pode acessar sua conta normalmente utilizando sua nova senha.", "successButton": "Ir para Início"}, "accountActivation": {"title": "Ativar Conta", "firstStepDescription": "Enviamos o código de ativação para o e-mail {{email}}", "secondStepDescription": "Preencha os campos abaixo para definir sua senha e ativar sua conta.", "securityCode": "Insira o código de segurança", "newPassword": "Nova senha", "repeatNewPassword": "<PERSON>etir nova senha", "passwordRequirement": "Obs.: <PERSON><PERSON> senha deve ter ao menos 8 caracteres.", "newPasswordPlaceholder": "Insira a nova senha", "repeatNewPasswordPlaceholder": "Insira a senha novamente", "didNotReceiveCode": "Não recebeu o código?", "resendCode": "Reenviar código", "nextButton": "Prosseguir", "activateButton": "Ativar Conta", "codeResent": "<PERSON><PERSON><PERSON> reenviado", "codeResentDescription": "Um novo código de ativação foi enviado para seu e-mail."}, "accountActivationSuccess": {"successTitle": "Conta ativada com sucesso!", "successDescription": "Sua conta foi ativada com sucesso.\nAgora, você pode acessar sua conta normalmente utilizando sua nova senha.", "successButton": "<PERSON>r para <PERSON>gin"}, "accountActivationNewPassword": {"title": "<PERSON><PERSON><PERSON>", "description": "Defina uma senha segura para sua conta.", "newPassword": "Nova senha", "confirmPassword": "Confirmar <PERSON><PERSON><PERSON>", "newPasswordPlaceholder": "Insira a nova senha", "confirmPasswordPlaceholder": "Confirme a senha", "passwordRequirement": "Obs.: <PERSON><PERSON> senha deve ter ao menos 8 caracteres.", "activateButton": "Ativar Conta", "errors": {"required": "Campo obrigatório", "minLength": "Mínimo 8 caracteres", "mismatch": "<PERSON><PERSON> n<PERSON>m"}}, "errors": {"invalidEmailFormat": "Formato de e-mail inválido", "requiredField": "Campo obrigatório", "unknownError": "<PERSON><PERSON>conhe<PERSON>", "invalidPasswordLength": "Senha deve ter no mínimo 8 caracteres", "invalidCodeSize": "<PERSON><PERSON><PERSON>", "invalidCpf": "CPF inválido", "emptyFields": "Erro ao acessar", "emptyFieldsDescription": "Preencha os campos abaixo para prosseguir com o acesso ao aplicativo", "forgetPasswordDescription": "Preencha o email para poder dar continuidade na recuperação de senha", "tryLater": "Ocorreu um erro desconhecido, por favor tente novamente mais tarde", "authenticationError": "Erro de autenticação", "authenticationErrorDescription": "Insira o código no campo abaixo para prosseguir com a redefinição da senha.", "passwordNotMatch": "As senhas não são identicas.", "passwordNotSecure": "Senha não cumpre requesitos de segurança", "passwordNotSecureDescription": "Sua senha não cumpre os requesitos minimos de segurança, para continuar forneça uma nova senha", "passwordMinimumLength": "Senha deve ter ao menos 8 caracteres.", "failedToRegisterFirebase": "Falha grave no aplicativo", "failedToRegisterFirebaseDescription": "Consulte o suporte para mais de<PERSON>hes", "generic": "Erro desconhecido. Tente novamente.", "invalidData": "<PERSON><PERSON>", "accountActivationDescription": "Verifique os dados inseridos e tente novamente.", "activationFailed": "Falha na ativação da conta", "resendFailed": "Falha ao reenviar código", "auth": {"unauthorized": "Não autorizado. Faça login novamente.", "forbidden": "Acesso negado. Você não tem permissão para este recurso.", "tokenExpired": "Sua sessão expirou. Faça login novamente.", "sessionExpired": "Sua sessão expirou. Faça login novamente."}, "validation": {"invalid": "Dados inválidos. Verifique as informações."}, "network": {"connection": "Erro de conexão. Verifique sua internet."}, "server": {"internal": "Erro interno do servidor. Tente novamente mais tarde.", "unavailable": "Serviço temporariamente indisponível. Tente novamente.", "gateway": "Erro de gateway. Tente novamente."}, "rateLimit": {"exceeded": "Muitas tentativas. Aguarde um momento.", "retryAfter": "Muitas tentativas. Aguarde {{seconds}} segundos."}, "boundary": {"title": "Ops! Algo deu errado"}, "reportTitle": "<PERSON><PERSON>", "reportMessage": "Deseja reportar este erro para nossa equipe?", "reportSuccess": "Erro Reportado", "reportSuccessMessage": "Obrigado! O erro foi reportado para nossa equipe."}, "tabBar": {"home": "Home", "schedule": "Agenda", "products": "<PERSON><PERSON><PERSON>", "profile": "<PERSON><PERSON>", "messages": "Mensagens"}, "schedule": {"title": "Agenda", "options": {"calendar": {"title": "<PERSON><PERSON><PERSON><PERSON>", "subtitle": "Você tem", "endSubtitle": "agendados.", "event": "eventos"}, "tickets": {"title": "<PERSON><PERSON> ingressos", "subtitle": "Você tem", "endSubtitle": "reservados.", "event": "ingressos"}, "eventList": {"title": "Lista de eventos", "subtitle": "Confira os eventos que estão acontecendo próximo de você."}, "createEventButton": "Criar novo evento"}}, "calendar": {"title": "<PERSON><PERSON><PERSON><PERSON>", "yearModal": {"title": "Selecionar ano", "confirmButton": "Selecionar ano", "closeButton": "<PERSON><PERSON><PERSON>"}, "eventsToday": "Eventos agendados do dia", "searchTextFieldPlaceholder": "Buscar nome do evento, etc...", "eventTypes": {"yourAndPrivate": "Seu evento (Privado)", "public": "Público", "free": "EVENTO GRATUITO", "paid": "EVENTO PAGO"}, "eventPresence": {"iWillGo": "ESTAREI LÁ", "notGoing": "NÃO VOU"}, "filters": {"eventTypes": "Tipos de Evento", "freeEvents": "Eventos Gratuitos", "attendingEvents": "Eventos que Vou Participar"}, "loadingEvents": "Carregando eventos...", "errorLoadingEvents": "Erro ao carregar eventos. Tente novamente."}, "products": {"title": "<PERSON><PERSON><PERSON>", "priceLabel": "Por:", "openProductButton": "VER PRODUTO", "detailsTitle": "Detalhes do produto", "free": "<PERSON><PERSON><PERSON><PERSON>", "inactive": "Inativo", "categories": {"title": "Categorias", "ebooks": "E-books", "courses": "Cursos", "meetings": "Reuniões", "groupMeetings": "Encontros", "mentoring": "Mentorias", "services": "Serviços"}, "periodicity": {"oneTime": "Compra única", "monthly": "<PERSON><PERSON><PERSON><PERSON> mensal", "yearly": "Assinatura anual"}, "partners": {"title": "Parceiros e benefícios"}}, "productPage": {"seeMore": "Ver mais", "seeLess": "<PERSON>er menos", "installments": "(parcele em até 12x sem juros).", "similarProducts": "<PERSON><PERSON><PERSON>"}, "eventList": {"title": "Lista de eventos", "category": "Categorias", "categories": {"business": "<PERSON>eg<PERSON><PERSON><PERSON>", "startups": "Startups", "technology": "Tecnologia", "marketing": "Marketing", "sustainability": "Sustentabilidade"}, "sponsoredEvents": "Eventos patrocinados", "badges": {"freeEvent": "EVENTO GRATUITO", "business": "NEGÓCIOS"}, "viewEvent": "VER EVENTO", "payNow": "<PERSON><PERSON>", "from": "Por:", "price": "R$ {{price}}", "priceLabel": "Preço", "freeEvent": "Evento gratuito", "loadingEvents": "Carregando eventos...", "errorLoadingEvents": "Erro ao carregar eventos. Tente novamente.", "noEventsFound": "Nenhum evento encontrado com os filtros aplicados.", "noEvents": "Nenhum evento disponível no momento."}, "recommendedProducts": {"loadingMore": "Carregando mais produtos...", "loadingProducts": "Carregando produtos...", "noProducts": "Nenhum produto encontrado", "recommended": "Recomendados para você", "seeAll": "Ver todos", "searchPlaceholder": "Digite o nome do produto"}, "acquireButton": {"acquire": "Adquira por:"}, "user": {"opportunities": "Oportunidades", "claimedBadges": "<PERSON><PERSON>", "aboutMe": "Sobre mim", "publishedOpportunity": "publicou uma nova oportunidade de negócio em", "investmentValue": "Valor de investimento", "price": "R$ {{price}}", "seeDetails": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "seeMore": "Ver mais", "seeLess": "<PERSON>er menos", "editInfo": "<PERSON><PERSON> dad<PERSON>", "generalSettings": "<PERSON><PERSON><PERSON><PERSON> gera<PERSON>", "seals": "Selos Club M", "sealsError": "Erro ao carregar selos", "noSeals": "<PERSON>en<PERSON> selo encontrado", "startConversation": "Iniciar conversa", "memberSince": "Me<PERSON>ro desde {{date}}", "profile": {"title": "<PERSON><PERSON>", "edit": "<PERSON><PERSON>", "editTitle": "<PERSON><PERSON>", "editSubtitle": "Atualize suas informações pessoais", "editHelpText": "Mantenha seus dados atualizados para uma melhor experiência no Club M.", "basicInfo": "Informações Básicas", "name": "Nome", "email": "E-mail", "phone": "Telefone", "company": "Empresa", "memberSince": "<PERSON><PERSON><PERSON>", "socialMedia": "Redes Sociais", "statistics": "Estatísticas", "accountActions": "Ações da Conta", "changePassword": "<PERSON><PERSON><PERSON>", "notificationSettings": "Configurações de Notificação", "privacySettings": "Configurações de Privacidade", "notInformed": "Não informado", "success": "Sucesso", "error": "Erro", "updateSuccess": "Perfil atualizado com sucesso!", "updateError": "Erro ao atualizar perfil. Tente novamente.", "loadError": "Erro ao carregar perfil do usuário.", "placeholders": {"name": "Digite seu nome completo", "email": "Digite seu e-mail", "phone": "Digite seu telefone", "company": "Digite o nome da empresa"}, "validation": {"error": "Erro de Validação", "fixErrors": "Por favor, corrija os erros antes de continuar.", "nameRequired": "Nome é obrigatório", "nameMinLength": "Nome deve ter pelo menos 2 caracteres", "emailRequired": "E-mail é obrigatório", "emailInvalid": "E-mail inválido", "phoneInvalid": "Telefone deve estar no formato (XX) XXXXX-XXXX"}}, "stats": {"eventsAttended": "Eventos Participados", "eventsRegistered": "Eventos Inscritos", "totalPurchases": "Total de Compras", "totalSpent": "Total Gasto", "memberSince": "<PERSON><PERSON><PERSON>", "lastActivity": "Última Atividade", "loadError": "Erro ao carregar estatísticas"}, "socialMedia": {"noLinks": "Nenhuma rede social cadastrada", "addFirst": "Adicionar primeira rede social", "addNew": "Adicionar Nova Rede Social", "addNewMessage": "Funcionalidade em desenvolvimento", "error": "Erro", "cannotOpenLink": "Não foi possível abrir o link", "linkError": "Erro ao abrir link", "deleteError": "Erro ao excluir link da rede social", "confirmDelete": "Confirmar <PERSON>", "confirmDeleteMessage": "Deseja realmente remover o link do {{platform}}?"}, "opportunitiesTab": {"loadError": "Erro ao carregar oportunidades", "noOpportunities": "Tem nenhuma oportunidade disponível no momento"}}, "opportunities": {"title": "Oportunidades", "all": "<PERSON><PERSON>", "published": "Publicadas", "inProgress": "Em Andamento", "loading": "Carregando oportunidades...", "loadError": "Erro ao carregar oportunidades", "noOpportunities": "Nenhuma oportunidade encontrada", "publishedOpportunity": "publicou uma oportunidade", "investmentValue": "Valor de investimento", "segment": "Segmento", "stage": "<PERSON><PERSON><PERSON><PERSON> atual", "seeMore": "Ver mais", "seeLess": "<PERSON>er menos", "showInterest": "<PERSON>ho interesse", "contact": {"title": "Entrar em Contato", "message": "Como você gostaria de entrar em contato?", "profile": "Ver perfil"}, "share": {"title": "Compartilhar", "message": "Funcionalidade em desenvolvimento"}, "details": {"title": "<PERSON><PERSON><PERSON>"}, "interest": {"title": "Demonstrar Interesse", "message": "Deseja demonstrar interesse nesta oportunidade? O criador será notificado.", "confirm": "Sim, tenho interesse", "success": "Interesse registrado!", "successMessage": "Seu interesse foi registrado. O criador da oportunidade será notificado."}}, "objectives": {"redeem": "Resgatar", "title": "Objetivos diários", "seeMore": "Ver mais", "error": "Erro ao carregar objetivos", "noObjectives": "Nenhum objetivo encontrado"}, "myTickets": {"title": "<PERSON><PERSON> ingressos", "year": "2025", "reservedTickets": "Ingressos reservados ({{count}})", "searchPlaceholder": "Buscar nome do evento, etc...", "eventDate": "Data/horário do evento", "ticketCode": "Código do ingresso", "organizedBy": "Sediado por:", "viewDetails": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "noTickets": "Não tem tickets ainda", "noSearchResults": "Nenhum resultado encontrado", "loading": "Carregando...", "error": "Erro ao carregar tickets", "retry": "Tentar novamente"}, "ticketDetails": {"title": "Detalhes do ingresso", "organizedBy": "Sediado por:", "eventDateTime": "Data/horário do evento", "ticketCode": "Código do ingresso", "location": "Local", "status": "Status", "loading": "Carregando...", "error": "Erro ao carregar detalhes do ticket", "retry": "Tentar novamente", "codeCopied": "<PERSON><PERSON><PERSON> copiado", "codeCopiedMessage": "O código do ingresso foi copiado para a área de transferência", "unmarkAttendance": "<PERSON><PERSON><PERSON>"}, "messages": {"title": "Mensagens", "pinned": "Fixado", "searchPlaceholder": "Busque pergun<PERSON> ou palavras-chave", "noMessages": "Nenhuma conversa encontrada", "loadingMessages": "Carregando conversas...", "errorLoadingMessages": "Erro ao carregar conversas", "retryButton": "Tentar novamente"}, "chat": {"today": "Hoje", "yesterday": "Ontem", "messagePlaceholder": "Digite uma mensagem...", "loadingMessages": "Carregando mensagens...", "errorLoadingMessages": "Erro ao carregar mensagens", "sendingMessage": "Enviando...", "messageSent": "Mensagem enviada", "messageFailedToSend": "Não foi possível enviar a mensagem. Tente novamente.", "typing": {"single": "{{name}} está digitando", "double": "{{name1}} e {{name2}} estão digitando", "multiple": "{{name}} e mais {{count}} pessoas estão digitando"}, "onlineStatus": {"online": "Online", "offline": "Offline", "lastSeenNow": "Visto agora", "lastSeenMinutes": "Vist<PERSON> há {{count}} min", "lastSeenHours": "Visto há {{count}}h", "lastSeenDays": "Visto há {{count}}d"}, "actions": {"sendMessage": "Enviar mensagem", "openEmoji": "<PERSON><PERSON><PERSON> emojis", "attachFile": "Anexar arquivo", "viewFullScreen": "Visualizar em tela cheia"}}, "createEvent": {"title": "Criar evento", "private": "Privado", "fields": {"eventTitle": {"label": "Título do evento", "placeholder": "Insira o título do evento"}, "description": {"label": "Descrição", "placeholder": "Insira uma descrição..."}, "dateTime": {"label": "Data/horário do evento", "placeholder": "Selecionar data e horário do evento"}, "eventType": {"label": "Tipo de evento", "placeholder": "Selecione o tipo de evento"}, "inviteMembers": {"label": "<PERSON><PERSON><PERSON> memb<PERSON> (opcional)", "placeholder": "Selecione os membros"}}, "createdBy": "Criado por:", "createdByName": "Maria Aparecida", "visibleToPublic": "Visível ao público", "additionalInfoButton": "Inserir informações adicionais", "buttons": {"publish": "Publicar evento", "cancel": "<PERSON><PERSON><PERSON>"}}, "payment": {"defaultItemTitle": "<PERSON><PERSON>", "confirmPayment": "Confirmar <PERSON>", "error": {"title": "<PERSON>rro no Pagamento", "message": "Ocorreu um erro ao processar o pagamento. Tente novamente.", "noMethod": "Nenhum método de pagamento selecionado.", "noCard": "Nenhum cartão de crédito selecionado.", "invalidData": "Dados de pagamento inválidos. Verifique as informações e tente novamente.", "unauthorized": "Não autorizado. Faça login novamente.", "serverError": "Erro no servidor. Tente novamente em alguns minutos.", "eventNotFound": "Evento não encontrado ou não pôde ser carregado."}}, "paymentMethods": {"0": "Cartão de Crédito", "1": "<PERSON><PERSON><PERSON>", "2": "PIX", "3": "<PERSON><PERSON><PERSON><PERSON>", "pix": "PIX", "credit_card": "Cartão de Crédito", "debit_card": "Cartão de Débito", "boleto": "<PERSON><PERSON><PERSON>"}, "paymentConfirmation": {"status": {"completed": {"title": "Pagamento Aprovado!", "message": "Seu pagamento foi processado com sucesso."}, "confirmed": {"title": "Pagamento Confirmado!", "message": "Seu pagamento foi processado com sucesso."}, "created": {"title": "Pa<PERSON><PERSON> Criad<PERSON>", "message": "Seu pagamento foi criado e está sendo processado."}, "pending": {"title": "Pagamento Pendente", "message": "Aguardando confirmação do pagamento."}, "processing": {"title": "<PERSON><PERSON><PERSON>", "message": "Seu pagamento está sendo processado."}, "failed": {"title": "Pagamento Falhou", "message": "Não foi possível processar o pagamento."}, "unknown": {"title": "Status Desconhecido", "message": "Não foi possível determinar o status do pagamento."}, "cancelled": {"title": "Pagamento Cancelado", "message": "O pagamento foi cancelado."}, "expired": {"title": "Pagamento Expirado", "message": "O tempo para pagamento expirou."}, "boleto": {"title": "<PERSON><PERSON><PERSON><PERSON>", "message": "Processamento em até 3 dias úteis após o pagamento."}}, "details": {"title": "Detalhes do Pagamento", "amount": "Valor", "method": "<PERSON><PERSON><PERSON><PERSON>", "id": "ID do Pagamento", "date": "Data"}, "pix": {"title": "Pagamento PIX", "codeLabel": "Código PIX:", "copyCode": "<PERSON><PERSON>r <PERSON>", "confirmPayment": "Confirmar <PERSON>", "copied": {"title": "Código <PERSON>!", "message": "O código PIX foi copiado para a área de transferência."}}, "boleto": {"title": "<PERSON><PERSON><PERSON>", "instructions": "Use o código de barras ou abra o boleto para pagamento.", "openBoleto": "<PERSON><PERSON><PERSON>", "barcodeLabel": "Código de Barras:", "identificationFieldLabel": "Campo de Identificação:", "ourNumberLabel": "<PERSON><PERSON>:", "copyBarCode": "Copiar Có<PERSON>", "copied": {"title": "Código <PERSON>!", "message": "O código de barras foi copiado para a área de transferência."}, "error": {"title": "Erro", "message": "Não foi possível abrir o boleto."}}, "creditCard": {"instructions": "Seu cartão será cobrado automaticamente."}, "creditCardEmptyState": {"title": "Você ainda não adicionou um cartão", "description": "Com um cartão cadastrado, seus pagamentos ficam mais fáceis e rápidos. Que tal adicionar agora?", "buttonText": "Adicionar meu cartão"}, "cancelPayment": "<PERSON><PERSON><PERSON>", "backToEvents": "Voltar aos Eventos", "cancel": {"title": "<PERSON><PERSON><PERSON>", "message": "Tem certeza que deseja cancelar este pagamento?"}, "notFound": {"title": "Pagamento Não Encontrado", "message": "O pagamento solicitado não foi encontrado."}}, "common": {"cancel": "<PERSON><PERSON><PERSON>", "back": "Voltar", "retry": "Tentar Novamente", "report": "Reportar", "ok": "OK", "yes": "<PERSON>m", "no": "Não", "save": "<PERSON><PERSON>", "delete": "Excluir", "edit": "<PERSON><PERSON>", "close": "<PERSON><PERSON><PERSON>", "loading": "Carregando...", "error": "Erro", "success": "Sucesso", "next": "<PERSON><PERSON><PERSON><PERSON>"}}