/**
 * Estilos para a tela de confirmação de pagamento
 * Seguindo os padrões de design do projeto
 */

import {StyleSheet} from "react-native";
import stylesConstants from "../styles-constants";

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: stylesConstants.colors.mainBackground
  },

  scrollView: {},

  scrollContent: {
    padding: 20,
    paddingBottom: 100
  },

  statusContainer: {
    alignItems: "center",
    padding: 32,
    borderRadius: 16,
    marginBottom: 24
  },

  statusTitle: {
    fontSize: 24,
    fontWeight: "700",
    textAlign: "center",
    marginTop: 16,
    marginBottom: 8,
    fontFamily: stylesConstants.fonts.inter
  },

  statusMessage: {
    fontSize: 16,
    textAlign: "center",
    fontFamily: stylesConstants.fonts.inter
  },

  detailsContainer: {
    backgroundColor: stylesConstants.colors.secondaryBackground,
    borderRadius: 12,
    padding: 20,
    marginBottom: 24
  },

  detailsTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: stylesConstants.colors.textPrimary,
    marginBottom: 16,
    fontFamily: stylesConstants.fonts.inter
  },

  detailRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 12
  },

  detailLabel: {
    fontSize: 14,
    color: stylesConstants.colors.textSecondary,
    fontFamily: stylesConstants.fonts.inter
  },

  detailValue: {
    fontSize: 14,
    fontWeight: "500",
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.inter,
    textAlign: "right",
    flex: 1,
    marginLeft: 16
  },

  instructionsContainer: {
    backgroundColor: stylesConstants.colors.secondaryBackground,
    borderRadius: 12,
    padding: 20,
    marginBottom: 24
  },

  instructionHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 16
  },

  instructionTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: stylesConstants.colors.textPrimary,
    marginLeft: 12,
    fontFamily: stylesConstants.fonts.inter
  },

  instructionText: {
    fontSize: 14,
    color: stylesConstants.colors.textSecondary,
    lineHeight: 20,
    fontFamily: stylesConstants.fonts.inter
  },

  qrCodeContainer: {
    alignItems: "center",
    marginVertical: 20
  },

  qrCode: {
    width: 200,
    height: 200,
    borderRadius: 8
  },

  pixCodeContainer: {
    marginTop: 16
  },

  pixCodeLabel: {
    fontSize: 14,
    fontWeight: "500",
    color: stylesConstants.colors.textPrimary,
    marginBottom: 8,
    fontFamily: stylesConstants.fonts.inter
  },

  pixCode: {
    fontSize: 12,
    color: stylesConstants.colors.textSecondary,
    backgroundColor: stylesConstants.colors.mainBackground,
    padding: 12,
    borderRadius: 8,
    fontFamily: "monospace",
    marginBottom: 12
  },

  copyButton: {
    backgroundColor: stylesConstants.colors.brand.brand500,
    paddingVertical: 12
  },

  confirmButton: {
    backgroundColor: stylesConstants.colors.green400,
    marginTop: 16,
    paddingVertical: 12
  },

  boletoButton: {
    backgroundColor: stylesConstants.colors.alert400,
    marginTop: 16,
    paddingVertical: 12
  },

  barcodeContainer: {
    marginTop: 16
  },

  barcodeLabel: {
    fontSize: 14,
    fontWeight: "500",
    color: stylesConstants.colors.textPrimary,
    marginBottom: 8,
    fontFamily: stylesConstants.fonts.inter
  },

  barcode: {
    fontSize: 13,
    color: stylesConstants.colors.textPrimary,
    backgroundColor: stylesConstants.colors.white,
    padding: 16,
    borderRadius: 8,
    fontFamily: "monospace",
    borderWidth: 1,
    borderColor: stylesConstants.colors.border,
    lineHeight: 20,
    textAlign: "center"
  },

  actionButtons: {
    position: "absolute",
    bottom: 0,
    left: 0,
    right: 0,
    flexDirection: "row",
    padding: 20,
    backgroundColor: stylesConstants.colors.mainBackground,
    borderTopWidth: 1,
    borderTopColor: stylesConstants.colors.borderDefault,
    gap: 12
  },

  actionButton: {
    flex: 1,
    paddingVertical: 16,
    borderRadius: 8
  },

  primaryButton: {
    backgroundColor: stylesConstants.colors.brand.brand500
  },

  cancelButton: {
    backgroundColor: "transparent",
    borderWidth: 1
  },

  cancelButtonBorder: {
    borderColor: stylesConstants.colors.error500
  },

  errorContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 40
  },

  errorTitle: {
    fontSize: 20,
    fontWeight: "600",
    color: stylesConstants.colors.textPrimary,
    textAlign: "center",
    marginTop: 16,
    marginBottom: 8,
    fontFamily: stylesConstants.fonts.inter
  },

  errorMessage: {
    fontSize: 16,
    color: stylesConstants.colors.textSecondary,
    textAlign: "center",
    marginBottom: 24,
    fontFamily: stylesConstants.fonts.inter
  },

  backButton: {
    backgroundColor: stylesConstants.colors.brand.brand500,
    paddingHorizontal: 32
  },

  // Status colors
  successIcon: {
    color: stylesConstants.colors.green400
  },

  successText: {
    color: stylesConstants.colors.green400
  },

  successBackground: {
    backgroundColor: stylesConstants.colors.success50
  },

  pendingIcon: {
    color: stylesConstants.colors.alert400
  },

  pendingText: {
    color: stylesConstants.colors.alert400
  },

  pendingBackground: {
    backgroundColor: stylesConstants.colors.yellow400 + "20"
  },

  processingIcon: {
    color: stylesConstants.colors.brand.brand500
  },

  processingText: {
    color: stylesConstants.colors.brand.brand500
  },

  processingBackground: {
    backgroundColor: stylesConstants.colors.brand.brand25
  },

  errorIcon: {
    color: stylesConstants.colors.error500
  },

  errorText: {
    color: stylesConstants.colors.error500
  },

  errorBackground: {
    backgroundColor: stylesConstants.colors.errorBackground
  },

  // Estilos específicos para boleto
  boletoDetailsContainer: {
    marginTop: 20,
    backgroundColor: stylesConstants.colors.mainBackground,
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: stylesConstants.colors.border
  },

  boletoField: {
    marginBottom: 20
  },

  boletoFieldLabel: {
    fontSize: 14,
    fontWeight: "500",
    color: stylesConstants.colors.textPrimary,
    marginBottom: 4,
    fontFamily: stylesConstants.fonts.inter
  },

  boletoFieldValue: {
    fontSize: 14,
    color: stylesConstants.colors.textPrimary,
    backgroundColor: stylesConstants.colors.white,
    padding: 12,
    borderRadius: 8,
    fontFamily: stylesConstants.fonts.inter,
    borderWidth: 1,
    borderColor: stylesConstants.colors.border,
    fontWeight: "500"
  },

  loadingContainer: {
    alignItems: "center",
    padding: 16
  },

  loadingText: {
    fontSize: 14,
    color: stylesConstants.colors.textSecondary,
    fontFamily: stylesConstants.fonts.inter
  },

  // Estilos específicos para erro do boleto
  boletoErrorContainer: {
    backgroundColor: stylesConstants.colors.errorBackground,
    padding: 16,
    borderRadius: 8,
    marginTop: 16,
    borderWidth: 1,
    borderColor: stylesConstants.colors.error500 + "20"
  },

  boletoErrorText: {
    fontSize: 14,
    color: stylesConstants.colors.error500,
    fontFamily: stylesConstants.fonts.inter,
    textAlign: "center"
  }
});

export default styles;
