import {StyleSheet} from "react-native";
import stylesConstants from "../styles-constants";

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  tabsContainer: {
    paddingHorizontal: 24,
    marginBottom: 24
  },
  contentContainer: {
    flex: 1,
    paddingHorizontal: 24
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    paddingVertical: 40
  },
  loadingText: {
    color: stylesConstants.colors.textSecondary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 16,
    marginTop: 16
  },
  headerContainer: {
    alignItems: "center",
    marginBottom: 32
  },
  avatarContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    marginBottom: 12,
    backgroundColor: stylesConstants.colors.gray300,
    alignItems: "center",
    justifyContent: "center"
  },
  avatar: {
    width: "100%",
    height: "100%",
    borderRadius: 40
  },
  changePhotoText: {
    color: stylesConstants.colors.brand.primary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: 600,
    lineHeight: 20
  },
  formContainer: {
    gap: 24,
    marginBottom: 32
  },
  inputGroup: {
    gap: 8
  },
  label: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: 600,
    lineHeight: 20
  },
  passwordHint: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 12,
    fontWeight: 400,
    lineHeight: 18,
    marginTop: 8
  },
  requiredLabel: {
    color: stylesConstants.colors.error600
  },
  inputRow: {
    flexDirection: "row",
    gap: 12
  },
  inputHalf: {
    flex: 1
  },
  textArea: {
    minHeight: 100,
    textAlignVertical: "top",
    paddingTop: 12
  },
  characterCount: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 12,
    fontWeight: 400,
    lineHeight: 18,
    textAlign: "right",
    marginTop: 4
  },
  pickerContainer: {
    backgroundColor: stylesConstants.colors.inputBackground,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: stylesConstants.colors.borderDefault,
    paddingHorizontal: 12,
    paddingVertical: 12
  },
  pickerText: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: 400,
    lineHeight: 20
  },
  pickerPlaceholder: {
    color: stylesConstants.colors.textPrimary
  },
  socialLinksContainer: {
    gap: 12
  },
  socialLinkRow: {
    flexDirection: "row",
    alignItems: "center",
    gap: 12
  },
  socialIcon: {
    width: 24,
    height: 24
  },
  socialInput: {
    flex: 1
  },
  privacyContainer: {
    gap: 16
  },
  privacyItem: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingVertical: 8
  },
  privacyLabel: {
    color: stylesConstants.colors.fullWhite,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 14,
    fontWeight: 400,
    lineHeight: 20,
    flex: 1
  },
  privacyDescription: {
    color: stylesConstants.colors.textPrimary,
    fontFamily: stylesConstants.fonts.openSans,
    fontSize: 12,
    fontWeight: 400,
    lineHeight: 18,
    marginTop: 4
  },
  switch: {
    transform: [{scaleX: 0.8}, {scaleY: 0.8}]
  },
  buttonContainer: {
    gap: 16,
    marginBottom: 32
  },
  deleteAccountButton: {
    backgroundColor: "transparent",
    borderWidth: 1,
    borderColor: stylesConstants.colors.error600
  },
  deleteAccountText: {
    color: stylesConstants.colors.error600
  }
});

export default styles;
