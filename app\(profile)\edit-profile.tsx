import React, {useState, useCallback, useEffect} from "react";
import {
  Text,
  View,
  ScrollView,
  TouchableOpacity,
  Image,
  ColorValue,
  ActivityIndicator,
  Alert
} from "react-native";
import ScreenWithHeader from "@/components/screen-with-header";
import {useTranslation} from "react-i18next";
import {useRouter} from "expo-router";
import Input<PERSON>ield from "@/components/input-field";
import FullSizeButton from "@/components/full-size-button";
import InvisibleFullSizeButton from "@/components/invisible-full-size-button";
import UserIcon from "@/components/icons/user-icon";
import PhoneIcon from "@/components/icons/phone-icon";
import CalendarIcon from "@/components/icons/calendar-icon";
import LicenceThirdPartyIcon from "@/components/icons/licence-third-party-icon";
import PasswordIcon from "@/components/icons/password-icon";
import UserTabs from "@/components/user/user-tabs";
import {
  useCurrentUser,
  useUpdateProfile,
  useChangePassword
} from "@/hooks/api/use-users";
import stylesConstants from "@/styles/styles-constants";
import {useBottomModal} from "@/contexts/bottom-modal-context";
import BottomModal from "@/components/bottom-modal";
import styles from "@/styles/profile/edit-profile.style";
import EmailIcon from "@/components/icons/email-icon";

interface EditableProfile {
  name: string;
  email: string;
  cpf: string;
  phone: string;
  birthDate: string;
}

interface Tab {
  title: string;
  id: number;
}

enum ProfileTabsEnum {
  PersonalData = 1,
  ChangePassword = 2
}

interface PhotoSelectionModalProps {
  onSelectFromGallery: () => void;
  onTakePhoto: () => void;
  onCancel: () => void;
}

const PhotoSelectionModal: React.FC<PhotoSelectionModalProps> = ({
  onSelectFromGallery,
  onTakePhoto,
  onCancel
}) => {
  const {t} = useTranslation();

  return (
    <BottomModal.Container>
      <BottomModal.ButtonsContainer>
        <FullSizeButton
          text={t("profile.selectFromGallery", "Selecionar da galeria")}
          onPress={onSelectFromGallery}
        />
        <InvisibleFullSizeButton
          text={t("profile.takePhoto", "Tirar uma foto")}
          onPress={onTakePhoto}
        />
        <InvisibleFullSizeButton
          text={t("profile.cancel", "Cancelar")}
          onPress={onCancel}
        />
      </BottomModal.ButtonsContainer>
    </BottomModal.Container>
  );
};

const EditProfile: React.FC = () => {
  const {t} = useTranslation();
  const router = useRouter();
  const {openModal, closeModal} = useBottomModal();
  const [activeTab, setActiveTab] = useState(ProfileTabsEnum.PersonalData);

  // Hooks para dados do usuário
  const {data: user, isLoading: isLoadingUser} = useCurrentUser();
  const updateProfileMutation = useUpdateProfile();
  const changePasswordMutation = useChangePassword();

  // Estado local do formulário
  const [profile, setProfile] = useState<EditableProfile>({
    name: "",
    email: "",
    cpf: "",
    phone: "",
    birthDate: ""
  });

  const [avatar, setAvatar] = useState<string>("");
  const [currentPassword, setCurrentPassword] = useState("");
  const [newPassword, setNewPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");

  // Carregar dados do usuário quando disponível
  useEffect(() => {
    if (user) {
      setProfile({
        name: user.name || "",
        email: user.email || "",
        cpf: user.document || "",
        phone: user.phone || "",
        birthDate: user.birthDate || ""
      });
      setAvatar(user.avatar || "");
    }
  }, [user]);

  const tabs: Tab[] = [
    {
      title: t("profile.personalData", "Dados pessoais"),
      id: ProfileTabsEnum.PersonalData
    },
    {
      title: t("profile.changePassword", "Alterar senha"),
      id: ProfileTabsEnum.ChangePassword
    }
  ];

  const handleInputChange =
    (field: keyof EditableProfile) => (value: string) => {
      setProfile((prev) => ({
        ...prev,
        [field]: value
      }));
    };

  const handleTabChange = useCallback((id: number) => {
    setActiveTab(id);
  }, []);

  const handleSaveProfile = async () => {
    // Validate required fields
    if (!profile.name.trim()) {
      console.log("Nome é obrigatório");
      return;
    }

    if (!profile.email.trim()) {
      console.log("Email é obrigatório");
      return;
    }

    try {
      // Usar mutation para atualizar perfil
      await updateProfileMutation.mutateAsync({
        name: profile.name,
        email: profile.email,
        phone: profile.phone,
        avatar: avatar
      });

      console.log("Perfil atualizado com sucesso");
    } catch (error) {
      console.error("Erro ao atualizar perfil:", error);
    }
  };

  const handleChangePassword = async () => {
    // Validação dos campos obrigatórios
    if (
      !currentPassword.trim() ||
      !newPassword.trim() ||
      !confirmPassword.trim()
    ) {
      Alert.alert(
        t("profile.error", "Erro"),
        t("profile.allFieldsRequired", "Todos os campos são obrigatórios")
      );
      return;
    }

    // Validação de confirmação de senha
    if (newPassword !== confirmPassword) {
      Alert.alert(
        t("profile.error", "Erro"),
        t("profile.passwordsDoNotMatch", "As senhas não coincidem")
      );
      return;
    }

    // Validação de tamanho mínimo da senha
    if (newPassword.length < 8) {
      Alert.alert(
        t("profile.error", "Erro"),
        t(
          "profile.passwordTooShort",
          "A senha deve ter pelo menos 8 caracteres"
        )
      );
      return;
    }

    try {
      // Chamar API para alterar senha
      await changePasswordMutation.mutateAsync({
        currentPassword,
        newPassword,
        confirmPassword
      });

      // Limpar campos após sucesso
      setCurrentPassword("");
      setNewPassword("");
      setConfirmPassword("");

      // Mostrar mensagem de sucesso
      Alert.alert(
        t("profile.success", "Sucesso"),
        t("profile.passwordChangedSuccessfully", "Senha alterada com sucesso!")
      );
    } catch (error) {
      console.error("Erro ao alterar senha:", error);

      // Mostrar mensagem de erro
      Alert.alert(
        t("profile.error", "Erro"),
        t(
          "profile.passwordChangeError",
          "Erro ao alterar senha. Verifique se a senha atual está correta."
        )
      );
    }
  };

  const handleChangePhoto = () => {
    openModal({
      title: "",
      children: (
        <PhotoSelectionModal
          onSelectFromGallery={() => {
            closeModal();
            console.log("Select from gallery");
          }}
          onTakePhoto={() => {
            closeModal();
            console.log("Take photo");
          }}
          onCancel={closeModal}
        />
      )
    });
  };

  const userIcon = useCallback(
    (errorColor?: ColorValue) => <UserIcon replaceColor={errorColor} />,
    []
  );

  const emailIcon = useCallback(() => <EmailIcon />, []);

  const phoneIcon = useCallback(
    (errorColor?: ColorValue) => <PhoneIcon replaceColor={errorColor} />,
    []
  );

  const calendarIcon = useCallback(
    (errorColor?: ColorValue) => <CalendarIcon replaceColor={errorColor} />,
    []
  );

  const documentIcon = useCallback(
    (errorColor?: ColorValue) => (
      <LicenceThirdPartyIcon replaceColor={errorColor} />
    ),
    []
  );
  const passwordIcon = useCallback(
    (errorColor?: ColorValue) => <PasswordIcon replaceColor={errorColor} />,
    []
  );

  return (
    <ScreenWithHeader
      screenTitle={t("profile.profileInfo", "Informações de perfil")}
      backButton
    >
      <View style={styles.container}>
        {/* Tabs */}
        <UserTabs
          tabs={tabs}
          currentTab={activeTab}
          onTabChange={handleTabChange}
          style={styles.tabsContainer}
        />

        <ScrollView style={styles.contentContainer}>
          {isLoadingUser ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator
                size="large"
                color={stylesConstants.colors.primary}
              />
              <Text style={styles.loadingText}>
                {t("common.loading", "Carregando...")}
              </Text>
            </View>
          ) : activeTab === ProfileTabsEnum.PersonalData ? (
            <>
              {/* Profile Photo */}
              <View style={styles.headerContainer}>
                <TouchableOpacity
                  style={styles.avatarContainer}
                  onPress={handleChangePhoto}
                >
                  <Image source={{uri: avatar}} style={styles.avatar} />
                </TouchableOpacity>
                <TouchableOpacity onPress={handleChangePhoto}>
                  <Text style={styles.changePhotoText}>
                    {t("profile.changePhoto", "Alterar foto")}
                  </Text>
                </TouchableOpacity>
              </View>

              {/* Form Fields */}
              <View style={styles.formContainer}>
                <View style={styles.inputGroup}>
                  <Text style={styles.label}>
                    {t("profile.fullName", "Nome completo")}
                  </Text>
                  <InputField
                    value={profile.name}
                    onChangeText={handleInputChange("name")}
                    placeholder={t("profile.enterName", "Maria Aparecida")}
                    icon={userIcon}
                  />
                </View>

                <View style={styles.inputGroup}>
                  <Text style={styles.label}>
                    {t("profile.email", "E-mail")}
                  </Text>
                  <InputField
                    value={profile.email}
                    onChangeText={handleInputChange("email")}
                    placeholder={t(
                      "profile.enterEmail",
                      "<EMAIL>"
                    )}
                    inputMode="email"
                    icon={emailIcon}
                  />
                </View>

                <View style={styles.inputGroup}>
                  <Text style={styles.label}>{t("profile.cpf", "CPF")}</Text>
                  <InputField
                    value={profile.cpf}
                    onChangeText={handleInputChange("cpf")}
                    placeholder={t("profile.enterCpf", "000.000.000-00")}
                    icon={documentIcon}
                  />
                </View>

                <View style={styles.inputGroup}>
                  <Text style={styles.label}>
                    {t("profile.phone", "Telefone")}
                  </Text>
                  <InputField
                    value={profile.phone}
                    onChangeText={handleInputChange("phone")}
                    placeholder={t("profile.enterPhone", "+55 47 0000-0000")}
                    icon={phoneIcon}
                    inputMode="tel"
                  />
                </View>

                <View style={styles.inputGroup}>
                  <Text style={styles.label}>
                    {t("profile.birthDate", "Data de nascimento")}
                  </Text>
                  <InputField
                    value={profile.birthDate}
                    onChangeText={handleInputChange("birthDate")}
                    placeholder={t("profile.enterBirthDate", "07/04/1976")}
                    icon={calendarIcon}
                  />
                </View>
              </View>

              {/* Buttons */}
              <View style={styles.buttonContainer}>
                <FullSizeButton
                  text={t("profile.saveChanges", "Salvar alterações")}
                  onPress={handleSaveProfile}
                />
                <InvisibleFullSizeButton
                  text={t("profile.backToProfile", "Voltar para perfil")}
                  onPress={() => router.back()}
                />
              </View>
            </>
          ) : (
            <>
              {/* Change Password Form */}
              <View style={styles.formContainer}>
                <View style={styles.inputGroup}>
                  <Text style={styles.label}>
                    {t("profile.currentPassword", "Senha atual")}
                  </Text>
                  <InputField
                    value={currentPassword}
                    onChangeText={setCurrentPassword}
                    placeholder={t(
                      "profile.enterCurrentPassword",
                      "Digite sua senha atual"
                    )}
                    isPassword
                    icon={passwordIcon}
                  />
                </View>

                <View style={styles.inputGroup}>
                  <Text style={styles.label}>
                    {t("profile.newPassword", "Nova senha")}
                  </Text>
                  <InputField
                    value={newPassword}
                    onChangeText={setNewPassword}
                    placeholder={t(
                      "profile.enterNewPassword",
                      "Digite sua nova senha"
                    )}
                    isPassword
                    icon={passwordIcon}
                  />
                </View>

                <View style={styles.inputGroup}>
                  <Text style={styles.label}>
                    {t("profile.confirmPassword", "Nova senha")}
                  </Text>
                  <InputField
                    value={confirmPassword}
                    onChangeText={setConfirmPassword}
                    placeholder={t(
                      "profile.enterConfirmPassword",
                      "Confirme sua nova senha"
                    )}
                    isPassword
                    icon={passwordIcon}
                  />
                </View>

                <Text style={styles.passwordHint}>
                  {t(
                    "profile.passwordHint",
                    "Sua senha deve conter ao menos 8 caracteres."
                  )}
                </Text>
              </View>

              {/* Password Change Buttons */}
              <View style={styles.buttonContainer}>
                <FullSizeButton
                  text={t("profile.changePassword", "Alterar senha")}
                  onPress={handleChangePassword}
                  disabled={changePasswordMutation.isPending}
                />
                <InvisibleFullSizeButton
                  text={t("profile.back", "Voltar")}
                  onPress={() => router.back()}
                />
              </View>
            </>
          )}
        </ScrollView>
      </View>
    </ScreenWithHeader>
  );
};

export default EditProfile;
