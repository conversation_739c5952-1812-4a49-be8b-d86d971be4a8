import React, {useMemo, useState} from "react";
import {View, Text} from "react-native";
import {Image} from "expo-image";
import {useTranslation} from "react-i18next";
import stylesConstants from "../../styles/styles-constants";
import Divider from "../divider";
import Button from "../button";
import styles from "../../styles/components/user/opportunity-card.style";
import Avatar from "./avatar";
import useCurrentLocalization from "../../hooks/use-localization";
import {expand} from "rxjs";

export interface OpportunityCardProps {
  id: number;
  title: string;
  avatarUrl: string;
  userName: string;
  description: string;
  imageUrl?: string;
  value: number;
  createdAt?: Date | string;
  onPress?: () => void;
}

const OpportunityCard: React.FC<OpportunityCardProps> = (props) => {
  const {t} = useTranslation();
  const localization = useCurrentLocalization();
  const [isExpanded, setIsExpanded] = useState(false);

  const value = useMemo(() => {
    return props.value
      ? (props.value / 100).toLocaleString(localization, {
          maximumFractionDigits: 2
        })
      : 0;
  }, [props.value, localization]);

  const truncatedDescription = useMemo(() => {
    return props.description.length > 150
      ? props.description.substring(0, 150) + "..."
      : props.description;
  }, [props.description]);

  const handleSeeMore = () => {
    setIsExpanded(!isExpanded);
  };

  return (
    <View style={styles.container}>
      <Avatar size={32} borderSize={2} url={props.avatarUrl} />
      <View style={styles.content}>
        <View style={styles.header}>
          <Text style={styles.name}>{props.userName}</Text>
          <Text style={styles.label}>{t("user.publishedOpportunity")}</Text>
          {props.createdAt && (
            <Text style={styles.date}>
              {new Date(props.createdAt).toLocaleDateString()}
              {", às "}
              {new Date(props.createdAt).toLocaleTimeString()}
            </Text>
          )}
        </View>
        {!!props.title && <Text style={styles.title}>{props.title}</Text>}
        <View>
          <Text
            style={isExpanded ? styles.description : styles.descriptionFree}
          >
            {props.description}
            <Text onPress={handleSeeMore} style={styles.seeMoreText}>
              {" "}
              {isExpanded ? "Ver Menos" : "Ver Mais"}
            </Text>
          </Text>
          <Image style={styles.image} source={props.imageUrl} />
          <Divider />
          <View style={styles.footer}>
            <View style={styles.valueContainer}>
              <Text style={styles.description}>
                {t("user.investmentValue")}
              </Text>
              <Text style={styles.value}>
                {t("user.price", {
                  price: value
                })}
              </Text>
            </View>
            <Button
              text={t("user.seeDetails")}
              backgroundColor={"transparent"}
              borderColor={stylesConstants.colors.gray25}
              style={styles.viewOpportunityButton}
              onPress={props.onPress}
            />
          </View>
        </View>
      </View>
    </View>
  );
};

export default OpportunityCard;
