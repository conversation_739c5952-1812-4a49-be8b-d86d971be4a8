/**
 * Tela de confirmação de pagamento
 * Exibe status do pagamento e próximos passos
 */

import React, {useCallback, useEffect, useMemo} from "react";
import {
  View,
  Text,
  ScrollView,
  Image,
  Alert,
  Linking,
  TouchableOpacity
} from "react-native";
import * as Clipboard from "expo-clipboard";
import {useTranslation} from "react-i18next";
import {useLocalSearchParams, useRouter} from "expo-router";
import {SafeAreaView} from "react-native-safe-area-context";

import Button from "../../components/button";
import LoadingOverlay from "../../components/loading-overlay";

import {PaymentStatus, PaymentType} from "../../models/api/payments.models";

import {
  usePayment,
  useCancelPayment,
  useBoletoDetails
} from "../../hooks/api/use-payments";

import styles from "../../styles/screens/payment-confirmation.style";
import CheckIcon from "../../components/icons/check-icon";
import ClockIcon from "../../components/icons/clock-icon";
import ErrorIcon from "../../components/icons/error-icon";
import PixIcon from "../../components/icons/pix-icon";
import BoletoIcon from "../../components/icons/boleto-icon";

const PaymentConfirmationScreen: React.FC = () => {
  const {t} = useTranslation();
  const router = useRouter();
  const params = useLocalSearchParams();

  const paymentId = params.paymentId as string;
  const paymentDataParam = params.paymentData as string;
  const skipApiCall = params.skipApiCall === "true";

  console.log("🎯 [PAYMENT-CONFIRMATION] Tela de confirmação iniciada");
  console.log("📋 [PAYMENT-CONFIRMATION] Payment ID:", paymentId);
  console.log("📋 [PAYMENT-CONFIRMATION] Skip API Call:", skipApiCall);

  // Parse dos dados do pagamento se fornecidos
  const paymentFromParams = useMemo(() => {
    if (paymentDataParam && skipApiCall) {
      try {
        const parsed = JSON.parse(paymentDataParam);
        console.log(
          "✅ [PAYMENT-CONFIRMATION] Usando dados dos parâmetros:",
          parsed
        );
        return parsed;
      } catch (error) {
        console.error(
          "❌ [PAYMENT-CONFIRMATION] Erro ao parsear dados:",
          error
        );
        return null;
      }
    }
    return null;
  }, [paymentDataParam, skipApiCall]);

  // Hooks de API - só executa se não tiver dados dos parâmetros
  const {
    data: paymentFromApi,
    isLoading,
    refetch
  } = usePayment(paymentId, {
    enabled: !skipApiCall && !!paymentId
  });
  const cancelPaymentMutation = useCancelPayment();

  // Usa dados dos parâmetros ou da API
  const payment = paymentFromParams || paymentFromApi;

  // Hook para buscar detalhes do boleto (só executa se for pagamento por boleto)
  const {
    data: boletoDetails,
    isLoading: isLoadingBoleto,
    error: boletoError
  } = useBoletoDetails(
    paymentId,
    payment?.type === PaymentType.Boleto && !!paymentId
  );

  console.log("🔍 [PAYMENT-CONFIRMATION] Estado dos dados:", {
    hasPaymentFromParams: !!paymentFromParams,
    hasPaymentFromApi: !!paymentFromApi,
    finalPayment: !!payment,
    paymentId: payment?.id,
    paymentStatus: payment?.status,
    paymentType: payment?.type,
    isLoading,
    skipApiCall
  });

  // Atualiza status do pagamento periodicamente para PIX
  useEffect(() => {
    if (
      payment?.type === PaymentType.Pix &&
      payment?.status === PaymentStatus.Pending
    ) {
      const interval = setInterval(() => {
        refetch();
      }, 5000); // Verifica a cada 5 segundos

      return () => clearInterval(interval);
    }
  }, [payment, refetch]);

  // Configuração baseada no status do pagamento
  const statusConfig = useMemo(() => {
    console.log("🔍 [PAYMENT-CONFIRMATION] 🔧 DEBUG statusConfig:", {
      hasPayment: !!payment,
      paymentStatus: payment?.status,
      paymentStatusType: typeof payment?.status,
      PaymentStatusConfirmed: PaymentStatus.Confirmed,
      PaymentStatusPending: PaymentStatus.Pending,
      PaymentStatusCreated: PaymentStatus.Created,
      isConfirmed: payment?.status === PaymentStatus.Confirmed,
      isPending: payment?.status === PaymentStatus.Pending,
      isCreated: payment?.status === PaymentStatus.Created
    });

    if (!payment) return null;

    switch (payment.status) {
      case PaymentStatus.Confirmed:
        return {
          icon: (
            <CheckIcon
              width={64}
              height={64}
              replaceColor={styles.successIcon.color}
            />
          ),
          title: t("paymentConfirmation.status.confirmed.title"),
          message: t("paymentConfirmation.status.confirmed.message"),
          color: styles.successText.color,
          backgroundColor: styles.successBackground.backgroundColor
        };

      case PaymentStatus.Pending:
      case PaymentStatus.Created:
        return {
          icon: <ClockIcon width={64} height={64} />,
          title: t("paymentConfirmation.status.pending.title"),
          message: t("paymentConfirmation.status.pending.message"),
          color: styles.pendingText.color,
          backgroundColor: styles.pendingBackground.backgroundColor
        };

      case PaymentStatus.Failed:
      case PaymentStatus.Unknown:
        return {
          icon: <ErrorIcon width={64} height={64} />,
          title: t(`paymentConfirmation.status.${payment.status}.title`),
          message: t(`paymentConfirmation.status.${payment.status}.message`),
          color: styles.errorText.color,
          backgroundColor: styles.errorBackground.backgroundColor
        };

      default:
        return null;
    }
  }, [payment, t]);

  // Formata valor monetário
  const formatCurrency = useCallback((value: number) => {
    return new Intl.NumberFormat("pt-BR", {
      style: "currency",
      currency: "BRL"
    }).format(value);
  }, []);

  // Handlers
  const handleCopyPixCode = useCallback(async () => {
    if (payment?.pixQrCode) {
      // Implementar cópia para clipboard
      Alert.alert(
        t("paymentConfirmation.pix.copied.title"),
        t("paymentConfirmation.pix.copied.message"),
        [{text: t("common.ok")}]
      );
    }
  }, [payment, t]);

  const handleOpenBoleto = useCallback(async () => {
    if (payment?.boletoUrl) {
      try {
        await Linking.openURL(payment.boletoUrl);
      } catch (error) {
        Alert.alert(
          t("paymentConfirmation.boleto.error.title"),
          t("paymentConfirmation.boleto.error.message"),
          [{text: t("common.ok")}]
        );
      }
    }
  }, [payment, t]);

  // Removido - não há mais confirmação manual de PIX

  const handleCancelPayment = useCallback(async () => {
    Alert.alert(
      t("paymentConfirmation.cancel.title"),
      t("paymentConfirmation.cancel.message"),
      [
        {text: t("common.no"), style: "cancel"},
        {
          text: t("common.yes"),
          style: "destructive",
          onPress: async () => {
            try {
              await cancelPaymentMutation.mutateAsync(paymentId);
              router.back();
            } catch (error) {
              console.error("Erro ao cancelar pagamento:", error);
            }
          }
        }
      ]
    );
  }, [cancelPaymentMutation, paymentId, router, t]);

  const handleBackToEvents = useCallback(() => {
    router.push("/(logged-stack)/event-list");
  }, [router]);

  // Handlers específicos para boleto
  const handleCopyBoletoBarCode = useCallback(async () => {
    if (boletoDetails?.barCode) {
      try {
        await Clipboard.setStringAsync(boletoDetails.barCode);
        Alert.alert(
          t("paymentConfirmation.boleto.copied.title"),
          t("paymentConfirmation.boleto.copied.message"),
          [{text: t("common.ok")}]
        );
      } catch (error) {
        Alert.alert(
          t("paymentConfirmation.boleto.error.title"),
          t("paymentConfirmation.boleto.error.message"),
          [{text: t("common.ok")}]
        );
      }
    }
  }, [boletoDetails, t]);

  const handleCopyBoletoField = useCallback(
    async (value: string, fieldName: string) => {
      try {
        await Clipboard.setStringAsync(value);
        Alert.alert(
          t("paymentConfirmation.boleto.copied.title"),
          t("paymentConfirmation.boleto.copied.message"),
          [{text: t("common.ok")}]
        );
      } catch (error) {
        Alert.alert(
          t("paymentConfirmation.boleto.error.title"),
          t("paymentConfirmation.boleto.error.message"),
          [{text: t("common.ok")}]
        );
      }
    },
    [t]
  );

  const handleOpenBoletoDocument = useCallback(async () => {
    if (payment?.boletoUrl) {
      try {
        await Linking.openURL(payment.boletoUrl);
      } catch (error) {
        Alert.alert(
          t("paymentConfirmation.boleto.error.title"),
          t("paymentConfirmation.boleto.error.message"),
          [{text: t("common.ok")}]
        );
      }
    }
  }, [payment, t]);

  // Renderiza instruções específicas do método de pagamento
  const renderPaymentInstructions = () => {
    if (!payment) return null;

    switch (payment.type) {
      case PaymentType.Pix:
        return (
          <View style={styles.instructionsContainer}>
            <View style={styles.instructionHeader}>
              <PixIcon size={32} />
              <Text style={styles.instructionTitle}>
                {t("paymentConfirmation.pix.title")}
              </Text>
            </View>

            {payment.pixQrCodeBase64 && (
              <View style={styles.qrCodeContainer}>
                <Image
                  source={{
                    uri: `data:image/png;base64,${payment.pixQrCodeBase64}`
                  }}
                  style={styles.qrCode}
                />
              </View>
            )}

            {payment.pixQrCode && (
              <View style={styles.pixCodeContainer}>
                <Text style={styles.pixCodeLabel}>
                  {t("paymentConfirmation.pix.codeLabel")}
                </Text>
                <Text style={styles.pixCode}>{payment.pixQrCode}</Text>
                <Button
                  text="paymentConfirmation.pix.copyCode"
                  onPress={handleCopyPixCode}
                  style={styles.copyButton}
                />
              </View>
            )}

            <Text style={styles.instructionText}>
              {t("paymentConfirmation.pix.instructions")}
            </Text>
          </View>
        );

      case PaymentType.Boleto:
        return (
          <View style={styles.instructionsContainer}>
            <View style={styles.instructionHeader}>
              <BoletoIcon size={32} />
              <Text style={styles.instructionTitle}>
                {t("paymentConfirmation.boleto.title")}
              </Text>
            </View>

            <Text style={styles.instructionText}>
              {t("paymentConfirmation.boleto.instructions")}
            </Text>

            {/* Detalhes específicos do boleto da API */}
            {boletoDetails && (
              <View style={styles.boletoDetailsContainer}>
                {/* Campo de Identificação */}
                {boletoDetails.identificationField && (
                  <View style={styles.boletoField}>
                    <Text style={styles.boletoFieldLabel}>
                      {t("paymentConfirmation.boleto.identificationFieldLabel")}
                    </Text>
                    <TouchableOpacity
                      onPress={() =>
                        handleCopyBoletoField(
                          boletoDetails.identificationField,
                          "identificationField"
                        )
                      }
                    >
                      <Text style={styles.boletoFieldValue}>
                        {boletoDetails.identificationField}
                      </Text>
                    </TouchableOpacity>
                  </View>
                )}

                {/* Nosso Número */}
                {boletoDetails.ourNumber && (
                  <View style={styles.boletoField}>
                    <Text style={styles.boletoFieldLabel}>
                      {t("paymentConfirmation.boleto.ourNumberLabel")}
                    </Text>
                    <TouchableOpacity
                      onPress={() =>
                        handleCopyBoletoField(
                          boletoDetails.ourNumber,
                          "ourNumber"
                        )
                      }
                    >
                      <Text style={styles.boletoFieldValue}>
                        {boletoDetails.ourNumber}
                      </Text>
                    </TouchableOpacity>
                  </View>
                )}

                {/* Código de Barras */}
                {boletoDetails.barCode && (
                  <View style={styles.barcodeContainer}>
                    <Text style={styles.barcodeLabel}>
                      {t("paymentConfirmation.boleto.barcodeLabel")}
                    </Text>
                    <Text style={styles.barcode}>{boletoDetails.barCode}</Text>
                    <Button
                      text="paymentConfirmation.boleto.copyBarCode"
                      onPress={handleCopyBoletoBarCode}
                      style={styles.copyButton}
                    />
                  </View>
                )}
              </View>
            )}

            {/* Loading dos detalhes do boleto */}
            {isLoadingBoleto && (
              <View style={styles.loadingContainer}>
                <Text style={styles.loadingText}>{t("common.loading")}</Text>
              </View>
            )}

            {/* Erro ao carregar detalhes do boleto */}
            {boletoError && (
              <View style={styles.boletoErrorContainer}>
                <Text style={styles.boletoErrorText}>
                  {t("paymentConfirmation.boleto.error.message")}
                </Text>
              </View>
            )}

            {/* Botão para abrir boleto (se disponível) */}
            {payment.boletoUrl && (
              <Button
                text="paymentConfirmation.boleto.openBoleto"
                onPress={handleOpenBoletoDocument}
                style={styles.boletoButton}
              />
            )}

            {/* Fallback: código de barras do payment (se não tiver detalhes da API) */}
            {!boletoDetails && payment.boletoBarcode && (
              <View style={styles.barcodeContainer}>
                <Text style={styles.barcodeLabel}>
                  {t("paymentConfirmation.boleto.barcodeLabel")}
                </Text>
                <Text style={styles.barcode}>{payment.boletoBarcode}</Text>
              </View>
            )}
          </View>
        );

      case PaymentType.CreditCard:
        return (
          <View style={styles.instructionsContainer}>
            <Text style={styles.instructionText}>
              {t("paymentConfirmation.creditCard.instructions")}
            </Text>
          </View>
        );

      default:
        return null;
    }
  };

  // Só mostra loading se não tiver dados dos parâmetros e estiver carregando da API
  if (isLoading && !paymentFromParams) {
    return <LoadingOverlay />;
  }

  if (!payment || !statusConfig) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.errorContainer}>
          <ErrorIcon width={64} height={64} />
          <Text style={styles.errorTitle}>
            {t("paymentConfirmation.notFound.title")}
          </Text>
          <Text style={styles.errorMessage}>
            {t("paymentConfirmation.notFound.message")}
          </Text>
          <Button
            text="common.back"
            onPress={() => router.back()}
            style={styles.backButton}
          />
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {/* Status do pagamento */}
        <View
          style={[
            styles.statusContainer,
            {backgroundColor: statusConfig.backgroundColor}
          ]}
        >
          {statusConfig.icon}
          <Text style={[styles.statusTitle, {color: statusConfig.color}]}>
            {statusConfig.title}
          </Text>
          <Text style={[styles.statusMessage, {color: statusConfig.color}]}>
            {statusConfig.message}
          </Text>
        </View>

        {/* Detalhes do pagamento */}
        <View style={styles.detailsContainer}>
          <Text style={styles.detailsTitle}>
            {t("paymentConfirmation.details.title")}
          </Text>

          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>
              {t("paymentConfirmation.details.amount")}
            </Text>
            <Text style={styles.detailValue}>
              {formatCurrency((payment.value || payment.amount || 0) / 100)}
            </Text>
          </View>

          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>
              {t("paymentConfirmation.details.method")}
            </Text>
            <Text style={styles.detailValue}>
              {t(`paymentMethods.${payment.type}`)}
            </Text>
          </View>

          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>
              {t("paymentConfirmation.details.id")}
            </Text>
            <Text style={styles.detailValue}>{payment.id}</Text>
          </View>

          {payment.createdAt && (
            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>
                {t("paymentConfirmation.details.date")}
              </Text>
              <Text style={styles.detailValue}>
                {new Date(payment.createdAt).toLocaleString("pt-BR")}
              </Text>
            </View>
          )}
        </View>

        {/* Instruções específicas do método */}
        {renderPaymentInstructions()}
      </ScrollView>

      {/* Botões de ação */}
      <View style={styles.actionButtons}>
        {payment.status === PaymentStatus.Pending && (
          <Button
            text="paymentConfirmation.cancelPayment"
            onPress={handleCancelPayment}
            style={[styles.actionButton, styles.cancelButton]}
            backgroundColor="transparent"
            borderColor={styles.cancelButtonBorder.borderColor}
          />
        )}

        <Button
          text={
            payment.status === PaymentStatus.Confirmed
              ? "paymentConfirmation.backToEvents"
              : "common.back"
          }
          onPress={
            payment.status === PaymentStatus.Confirmed
              ? handleBackToEvents
              : () => router.back()
          }
          style={[styles.actionButton, styles.primaryButton]}
        />
      </View>

      {cancelPaymentMutation.isPending && <LoadingOverlay />}
    </SafeAreaView>
  );
};

export default PaymentConfirmationScreen;
